import { useState, useRef, useEffect, useContext } from "react";
import TimeSlotGrid from "../TimeSlotGrid";
import { useForm } from "react-hook-form";
import { InteractiveButton } from "Components/InteractiveButton";
import { useClub } from "Context/Club";
import { showToast, GlobalContext } from "Context/Global";
import { MdCleaningServices } from "react-icons/md";

const days = [
  "Monday",
  "Tuesday",
  "Wednesday",
  "Thursday",
  "Friday",
  "Saturday",
  "Sunday",
];

// Utility functions for club hours and exceptions

// Check if a specific time is within club opening hours
const isTimeInClubHours = (time, clubTimes) => {
  if (!clubTimes || clubTimes.length === 0) return true;

  const [hours, minutes] = time.split(":");
  const timeInMinutes = parseInt(hours) * 60 + parseInt(minutes);

  return clubTimes.some((slot) => {
    const [fromHours, fromMinutes] = slot.from.split(":");
    const [untilHours, untilMinutes] = slot.until.split(":");

    const fromInMinutes = parseInt(fromHours) * 60 + parseInt(fromMinutes);
    let untilInMinutes = parseInt(untilHours) * 60 + parseInt(untilMinutes);

    // Handle midnight crossover: if until time is 00:00:00, treat it as end of day (24:00 = 1440 minutes)
    if (untilInMinutes === 0 && (untilHours === "00" || untilHours === "0")) {
      untilInMinutes = 24 * 60; // 1440 minutes = 24:00
    }

    // Handle cases where the time range spans across midnight
    if (untilInMinutes <= fromInMinutes) {
      // Time range spans midnight (e.g., 22:00 to 02:00)
      return timeInMinutes >= fromInMinutes || timeInMinutes < untilInMinutes;
    } else {
      // Normal time range within the same day
      return timeInMinutes >= fromInMinutes && timeInMinutes < untilInMinutes;
    }
  });
};

// Check if a specific day is a day off for the club
const isClubDayOff = (day, daysOff) => {
  if (!daysOff || daysOff.length === 0) return false;
  return daysOff.includes(day);
};

// Get exceptions for a specific day
const getDayExceptions = (day, exceptions) => {
  if (!exceptions || exceptions.length === 0) return [];

  const dayName = day.toLowerCase();

  return exceptions.reduce((acc, exception) => {
    const dayException = exception.days.find((d) => d.day === dayName);
    if (dayException) {
      acc.push({
        name: exception.name,
        timeslots: dayException.timeslots,
      });
    }
    return acc;
  }, []);
};

// Check if a specific time is an exception time
const isExceptionTime = (time, dayExceptions) => {
  if (!dayExceptions || dayExceptions.length === 0) return false;

  // Convert time to HH:MM:00 format to match exception format
  const timeToCheck = time.includes(":00", 5) ? time : `${time}:00`;

  return dayExceptions.some((exception) =>
    exception.timeslots.includes(timeToCheck)
  );
};

// Get exception name for a specific time
const getExceptionName = (time, dayExceptions) => {
  if (!dayExceptions || dayExceptions.length === 0) return null;

  // Convert time to HH:MM:00 format to match exception format
  const timeToCheck = time.includes(":00", 5) ? time : `${time}:00`;

  const exception = dayExceptions.find((exception) =>
    exception.timeslots.includes(timeToCheck)
  );

  return exception ? exception.name : null;
};

export default function SetupCoachWorkingHours({
  onNext,
  isSubmitting,
  selectedTimeSlot,
  setSelectedTimeSlot,
  originalAvailability,
  setOriginalAvailability,
  setValue: parentSetValue,
  defaultValues,
}) {
  const [selectedSlots, setSelectedSlots] = useState({});
  const { club } = useClub();
  const { dispatch: globalDispatch } = useContext(GlobalContext);

  // Parse club data for hours, days off, and exceptions
  const [clubData, setClubData] = useState({
    times: [],
    daysOff: [],
    exceptions: [],
  });

  // console.log(defaultValues);
  const {
    handleSubmit,
    formState: { errors },
  } = useForm();

  // Update the isSelected check to be more flexible
  const isSelected = (time, day) => {
    const daySlot = selectedTimeSlot?.find(
      (daySlot) => daySlot.day === day.toLowerCase()
    );
    if (!daySlot) return false;

    const timeWithoutSeconds = time.replace(":00", "");
    return daySlot.timeslots.some(
      (t) => t === time || t.replace(":00", "") === timeWithoutSeconds
    );
  };

  const handleTimeSelect = (time, day) => {
    // Check if the day is a club day off
    if (isClubDayOff(day, clubData.daysOff)) {
      showToast(globalDispatch, `${day} is a club day off`, 3000, "error");
      return;
    }

    // Check if the time is within club hours
    if (!isTimeInClubHours(time, clubData.times)) {
      showToast(
        globalDispatch,
        "This time is outside club hours",
        3000,
        "error"
      );
      return;
    }

    // Check if the time is an exception time
    const dayExceptions = getDayExceptions(day, clubData.exceptions);
    if (isExceptionTime(time, dayExceptions)) {
      const exceptionName = getExceptionName(time, dayExceptions);
      showToast(
        globalDispatch,
        `This time is marked as "${exceptionName || "Exception"}"`,
        3000,
        "warning"
      );
      // We still allow selection but show a warning
    }

    setSelectedTimeSlot((prev) => {
      return prev.map((daySlot) => {
        if (daySlot.day === day.toLowerCase()) {
          const timeWithoutSeconds = time.replace(":00", "");
          const timeExists = daySlot.timeslots.some(
            (t) => t === time || t === timeWithoutSeconds
          );
          if (!timeExists) {
            return {
              ...daySlot,
              timeslots: [...daySlot.timeslots, time].sort(),
            };
          }
        }
        return daySlot;
      });
    });
  };

  const handleDeleteTime = (time, day) => {
    setSelectedTimeSlot((prev) => {
      return prev.map((daySlot) => {
        if (daySlot.day === day.toLowerCase()) {
          return {
            ...daySlot,
            timeslots: daySlot.timeslots.filter(
              (t) => t !== time && t !== time.replace(":00", "")
            ),
          };
        }
        return daySlot;
      });
    });
  };

  const formatSelectedTimes = () => {
    return selectedTimeSlot.filter((daySlot) => daySlot.timeslots.length > 0);
  };

  const onSave = async () => {
    try {
      const formattedTimes = formatSelectedTimes();
      parentSetValue("availability", formattedTimes);
      await onNext();
    } catch (error) {
      console.error("Error saving working hours:", error);
    }
  };
  const onSkip = async () => {
    parentSetValue("availability", []);
    await onNext();
  };

  // Parse club data when club changes
  useEffect(() => {
    if (club) {
      const times = club.times ? JSON.parse(club.times) : [];
      const daysOff = club.days_off ? JSON.parse(club.days_off) : [];
      const exceptions = club.exceptions ? JSON.parse(club.exceptions) : [];
      setClubData({ times, daysOff, exceptions });
    }
  }, [club]);

  return (
    <div className="mx-auto w-full max-w-7xl px-4">
      <h1 className="mb-8 text-center text-2xl font-semibold">
        Set your working hours
      </h1>

      <div>
        <TimeSlotGrid
          days={days}
          isSelected={isSelected}
          handleTimeSelect={handleTimeSelect}
          handleDeleteTime={handleDeleteTime}
          renderTimeSlotContent={(timeSlot, day) => {
            // Check if day is a club day off
            if (isClubDayOff(day, clubData.daysOff)) {
              return (
                <div className="absolute inset-0 flex items-center justify-center bg-red-50 text-xs font-medium text-red-500">
                  Club Closed
                </div>
              );
            }

            // Check if time is outside club hours
            if (!isTimeInClubHours(timeSlot.value, clubData.times)) {
              return (
                <div className="absolute inset-0 flex items-center justify-center bg-red-50 text-xs font-medium text-red-500">
                  Club Closed
                </div>
              );
            }

            // Check if time is an exception
            const dayExceptions = getDayExceptions(day, clubData.exceptions);
            if (isExceptionTime(timeSlot.value, dayExceptions)) {
              const exceptionName = getExceptionName(
                timeSlot.value,
                dayExceptions
              );
              return (
                <div className="absolute inset-0 flex items-center justify-center bg-orange-50 text-xs font-medium text-orange-500">
                  <MdCleaningServices className="mr-1 hidden sm:inline" />
                  <span className="truncate">
                    {exceptionName || "Exception"}
                  </span>
                </div>
              );
            }

            return null;
          }}
          disableTimeSlot={(timeSlot, day) => {
            // Disable if day is a club day off
            if (isClubDayOff(day, clubData.daysOff)) {
              return true;
            }

            // Disable if time is outside club hours
            if (!isTimeInClubHours(timeSlot.value, clubData.times)) {
              return true;
            }

            // We don't disable exception times, just show a warning when selected
            return false;
          }}
        />

        {/* {Object.keys(selectedSlots).length === 0 && (
          <p className="mt-2 text-center text-sm text-red-500">
            Please select at least one time slot
          </p>
        )} */}

        <div className="mt-8 flex justify-center gap-4">
          <InteractiveButton
            type="button"
            onClick={onSkip}
            className="rounded-lg border border-gray-300 px-6 py-2 hover:bg-gray-50"
          >
            Skip
          </InteractiveButton>
          <InteractiveButton
            type="submit"
            onClick={onSave}
            loading={isSubmitting}
            disabled={formatSelectedTimes().length === 0}
            className="rounded-lg bg-primaryGreen px-6 py-2 text-white hover:bg-primaryGreen/80 disabled:opacity-50"
          >
            Continue
          </InteractiveButton>
        </div>
      </div>
    </div>
  );
}
