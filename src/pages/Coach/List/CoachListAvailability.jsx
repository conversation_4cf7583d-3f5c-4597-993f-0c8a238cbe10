import { InteractiveButton } from "Components/InteractiveButton";
import LoadingSpinner from "Components/LoadingSpinner";
import TimeSlotGrid from "Components/TimeSlotGrid";
import { showToast, GlobalContext } from "Context/Global";
import moment from "moment";
import { useContext, useEffect, useState } from "react";
import { FiHelpCircle } from "react-icons/fi";
import { Tooltip } from "react-tooltip";
import MkdSDK from "Utils/MkdSDK";
import DeleteModal from "Components/Modals/DeleteModal";
import { useClub } from "Context/Club";
import { MdCleaningServices } from "react-icons/md";

let sdk = new MkdSDK();

const days = [
  "Monday",
  "Tuesday",
  "Wednesday",
  "Thursday",
  "Friday",
  "Saturday",
  "Sunday",
];

// Utility functions for club hours and exceptions

// Check if a specific time is within club opening hours
const isTimeInClubHours = (time, clubTimes) => {
  if (!clubTimes || clubTimes.length === 0) return true;

  const [hours, minutes] = time.split(":");
  const timeInMinutes = parseInt(hours) * 60 + parseInt(minutes);

  return clubTimes.some((slot) => {
    const [fromHours, fromMinutes] = slot.from.split(":");
    const [untilHours, untilMinutes] = slot.until.split(":");

    const fromInMinutes = parseInt(fromHours) * 60 + parseInt(fromMinutes);
    let untilInMinutes = parseInt(untilHours) * 60 + parseInt(untilMinutes);

    // Handle midnight crossover: if until time is 00:00:00, treat it as end of day (24:00 = 1440 minutes)
    if (untilInMinutes === 0 && (untilHours === "00" || untilHours === "0")) {
      untilInMinutes = 24 * 60; // 1440 minutes = 24:00
    }

    // Handle cases where the time range spans across midnight
    if (untilInMinutes <= fromInMinutes) {
      // Time range spans midnight (e.g., 22:00 to 02:00)
      return timeInMinutes >= fromInMinutes || timeInMinutes < untilInMinutes;
    } else {
      // Normal time range within the same day
      return timeInMinutes >= fromInMinutes && timeInMinutes < untilInMinutes;
    }
  });
};

// Check if a specific day is a day off for the club
const isClubDayOff = (day, daysOff) => {
  if (!daysOff || daysOff.length === 0) return false;
  return daysOff.includes(day);
};

// Get exceptions for a specific day
const getDayExceptions = (day, exceptions) => {
  if (!exceptions || exceptions.length === 0) return [];

  const dayName = day.toLowerCase();

  return exceptions.reduce((acc, exception) => {
    const dayException = exception.days.find((d) => d.day === dayName);
    if (dayException) {
      acc.push({
        name: exception.name,
        timeslots: dayException.timeslots,
      });
    }
    return acc;
  }, []);
};

// Check if a specific time is an exception time
const isExceptionTime = (time, dayExceptions) => {
  if (!dayExceptions || dayExceptions.length === 0) return false;

  // Convert time to HH:MM:00 format to match exception format
  const timeToCheck = time.includes(":00", 5) ? time : `${time}:00`;

  return dayExceptions.some((exception) =>
    exception.timeslots.includes(timeToCheck)
  );
};

// Get exception name for a specific time
const getExceptionName = (time, dayExceptions) => {
  if (!dayExceptions || dayExceptions.length === 0) return null;

  // Convert time to HH:MM:00 format to match exception format
  const timeToCheck = time.includes(":00", 5) ? time : `${time}:00`;

  const exception = dayExceptions.find((exception) =>
    exception.timeslots.includes(timeToCheck)
  );

  return exception ? exception.name : null;
};

export default function CoachListAvailability() {
  const { dispatch: globalDispatch } = useContext(GlobalContext);
  const [weekCounter, setWeekCounter] = useState(0);
  const [currentWeek, setCurrentWeek] = useState(moment());
  const [savingTimeslot, setSavingTimeslot] = useState(false);
  const [savingDefaultTimeslot, setSavingDefaultTimeslot] = useState(false);
  const [tab, setTab] = useState("calendar");
  const [selectedTimeSlot, setSelectedTimeSlot] = useState([]);
  const [defaultTimeSlot, setDefaultTimeSlot] = useState([]);
  const [originalAvailability, setOriginalAvailability] = useState(null);
  const [originalDefaultAvailability, setOriginalDefaultAvailability] =
    useState(null);
  const [coachProfile, setCoachProfile] = useState(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isResetModalOpen, setIsResetModalOpen] = useState(false);
  const [isMobileView, setIsMobileView] = useState(window.innerWidth < 768);
  const { club } = useClub();
  console.log(club);
  // Parse club data for hours, days off, and exceptions
  const [clubData, setClubData] = useState({
    times: [],
    daysOff: [],
    exceptions: [],
  });
  // State to track week-specific availability
  const [weekSpecificAvailability, setWeekSpecificAvailability] = useState([]);
  // Track if current week's availability is customized
  const [isCustomizedWeek, setIsCustomizedWeek] = useState(false);

  // Function to check if there are changes to save
  const hasChanges = () => {
    if (!selectedTimeSlot || !originalAvailability) return false;

    // Get filtered versions of both arrays (only days with timeslots)
    const currentSelection = formatSelectedTimes();
    const originalFiltered = Array.isArray(originalAvailability)
      ? originalAvailability.filter(
          (day) => day.timeslots && day.timeslots.length > 0
        )
      : [];

    // If the current selection is empty and original has items, there are changes
    if (currentSelection.length === 0 && originalFiltered.length > 0)
      return true;

    // If the current selection has items and original is empty, there are changes
    if (currentSelection.length > 0 && originalFiltered.length === 0)
      return true;

    // If lengths are different, there are changes
    if (currentSelection.length !== originalFiltered.length) return true;

    // Create a map of days for faster lookup
    const originalDayMap = {};
    originalFiltered.forEach((day) => {
      originalDayMap[day.day.toLowerCase()] = [...day.timeslots].sort();
    });

    // Deep comparison of the timeslots
    for (const currentDay of currentSelection) {
      const dayName = currentDay.day.toLowerCase();
      const originalTimeslots = originalDayMap[dayName];

      // If this day doesn't exist in original, there are changes
      if (!originalTimeslots) return true;

      // If timeslot counts are different, there are changes
      if (currentDay.timeslots.length !== originalTimeslots.length) return true;

      // Sort current timeslots for consistent comparison
      const sortedCurrentTimeslots = [...currentDay.timeslots].sort();

      // Compare each timeslot
      for (let i = 0; i < sortedCurrentTimeslots.length; i++) {
        // Normalize timeslot format for comparison (remove seconds if present)
        const currentTime = sortedCurrentTimeslots[i].replace(/:00$/, "");
        const originalTime = originalTimeslots[i].replace(/:00$/, "");

        if (currentTime !== originalTime) return true;
      }
    }

    return false;
  };

  // Function to check if there are changes to save for default availability
  const hasDefaultChanges = () => {
    if (!defaultTimeSlot || !originalDefaultAvailability) return false;

    // Get filtered versions of both arrays (only days with timeslots)
    const currentSelection = formatDefaultTimes();
    const originalFiltered = Array.isArray(originalDefaultAvailability)
      ? originalDefaultAvailability.filter(
          (day) => day.timeslots && day.timeslots.length > 0
        )
      : [];

    // If the current selection is empty and original has items, there are changes
    if (currentSelection.length === 0 && originalFiltered.length > 0)
      return true;

    // If the current selection has items and original is empty, there are changes
    if (currentSelection.length > 0 && originalFiltered.length === 0)
      return true;

    // If lengths are different, there are changes
    if (currentSelection.length !== originalFiltered.length) return true;

    // Create a map of days for faster lookup
    const originalDayMap = {};
    originalFiltered.forEach((day) => {
      originalDayMap[day.day.toLowerCase()] = [...day.timeslots].sort();
    });

    // Deep comparison of the timeslots
    for (const currentDay of currentSelection) {
      const dayName = currentDay.day.toLowerCase();
      const originalTimeslots = originalDayMap[dayName];

      // If this day doesn't exist in original, there are changes
      if (!originalTimeslots) return true;

      // If timeslot counts are different, there are changes
      if (currentDay.timeslots.length !== originalTimeslots.length) return true;

      // Sort current timeslots for consistent comparison
      const sortedCurrentTimeslots = [...currentDay.timeslots].sort();

      // Compare each timeslot
      for (let i = 0; i < sortedCurrentTimeslots.length; i++) {
        // Normalize timeslot format for comparison (remove seconds if present)
        const currentTime = sortedCurrentTimeslots[i].replace(/:00$/, "");
        const originalTime = originalTimeslots[i].replace(/:00$/, "");

        if (currentTime !== originalTime) return true;
      }
    }

    return false;
  };

  // Get current week's identifier for storage
  const getCurrentWeekIdentifier = () => {
    return currentWeek.clone().startOf("week").format("YYYY-MM-DD");
  };

  // Find week specific availability by week identifier
  // This function is kept for reference but we now check the state directly in handleWeekSwitch

  async function fetchCoachProfile() {
    setIsLoading(true);
    try {
      const profile = await sdk.callRawAPI(
        "/v3/api/custom/courtmatchup/coach/profile",
        {},
        "GET"
      );
      const availability = JSON.parse(profile.availability);
      const default_availability = JSON.parse(profile.default_availability);
      const week_specific_availability = profile.week_specific_availability
        ? JSON.parse(profile.week_specific_availability)
        : [];

      // Initialize with all days having empty timeslots
      const initialAvailability = days.map((day) => ({
        day: day.toLowerCase(),
        timeslots: [],
      }));

      const defaultAvailability = days.map((day) => ({
        day: day.toLowerCase(),
        timeslots: [],
      }));

      // If there's existing availability, update the corresponding days
      if (
        availability &&
        Array.isArray(availability) &&
        availability.length > 0
      ) {
        availability.forEach((dayData) => {
          const index = initialAvailability.findIndex(
            (day) => day.day === dayData.day.toLowerCase()
          );
          if (index !== -1) {
            initialAvailability[index].timeslots = dayData.timeslots;
          }
        });
      }

      // update Default availability
      if (default_availability && Array.isArray(default_availability)) {
        default_availability.forEach((dayData) => {
          const index = defaultAvailability.findIndex(
            (day) => day.day === dayData.day.toLowerCase()
          );
          if (index !== -1) {
            defaultAvailability[index].timeslots = dayData.timeslots;
          }
        });
      }

      setDefaultTimeSlot(defaultAvailability);
      setOriginalDefaultAvailability(default_availability || []);
      setWeekSpecificAvailability(week_specific_availability);
      setCoachProfile(profile);

      // Load the appropriate availability for the current week
      loadAvailabilityForCurrentWeek(
        initialAvailability,
        week_specific_availability
      );
    } catch (error) {
      console.error(error);
    } finally {
      setIsLoading(false);
    }
  }

  // Load availability for the currently selected week
  const loadAvailabilityForCurrentWeek = (
    defaultAvailability,
    weekSpecificData = null
  ) => {
    const weekData = weekSpecificData || weekSpecificAvailability;
    const weekId = getCurrentWeekIdentifier();

    // Find specific data for this week
    const weekSpecific = weekData.find((week) => week.weekId === weekId);

    console.log(
      "loadAvailabilityForCurrentWeek - Week specific data found:",
      weekSpecific
    );

    // If we have specific data for this week, use it
    if (weekSpecific && weekSpecific.availability) {
      // Make a deep copy to avoid reference issues
      const weekSpecificCopy = JSON.parse(
        JSON.stringify(weekSpecific.availability)
      );
      console.log(
        "Setting custom schedule for week:",
        weekId,
        weekSpecificCopy
      );
      setSelectedTimeSlot(weekSpecificCopy);
      setOriginalAvailability(weekSpecificCopy);
      setIsCustomizedWeek(true);
    } else {
      // Otherwise use the default availability
      // Make a deep copy to avoid reference issues
      const defaultAvailabilityCopy = JSON.parse(
        JSON.stringify(defaultAvailability)
      );
      setSelectedTimeSlot(defaultAvailabilityCopy);
      setOriginalAvailability(defaultAvailabilityCopy);
      setIsCustomizedWeek(false);
    }
  };

  const handleTimeSelect = (time, day) => {
    // Check if the day is a club day off
    if (isClubDayOff(day, clubData.daysOff)) {
      showToast(globalDispatch, `${day} is a club day off`, 3000, "error");
      return;
    }

    // Check if the time is within club hours
    if (!isTimeInClubHours(time, clubData.times)) {
      showToast(
        globalDispatch,
        "This time is outside club hours",
        3000,
        "error"
      );
      return;
    }

    // Check if the time is an exception time
    const dayExceptions = getDayExceptions(day, clubData.exceptions);
    if (isExceptionTime(time, dayExceptions)) {
      const exceptionName = getExceptionName(time, dayExceptions);
      showToast(
        globalDispatch,
        `This time is marked as "${exceptionName || "Exception"}"`,
        3000,
        "warning"
      );
      // We still allow selection but show a warning
    }

    setSelectedTimeSlot((prev) => {
      const updatedSlots = prev.map((daySlot) => {
        if (daySlot.day === day.toLowerCase()) {
          const timeWithoutSeconds = time.replace(":00", "");
          const timeExists = daySlot.timeslots.some(
            (t) => t === time || t === timeWithoutSeconds
          );
          if (!timeExists) {
            return {
              ...daySlot,
              timeslots: [...daySlot.timeslots, time].sort(),
            };
          }
        }
        return daySlot;
      });

      // If this is a new day being added, ensure it exists in the array
      const dayExists = updatedSlots.some(
        (slot) => slot.day === day.toLowerCase()
      );
      if (!dayExists) {
        updatedSlots.push({
          day: day.toLowerCase(),
          timeslots: [time],
        });
      }

      return updatedSlots;
    });
  };

  const handleDefaultTimeSelect = (time, day) => {
    // Check if the day is a club day off
    if (isClubDayOff(day, clubData.daysOff)) {
      showToast(globalDispatch, `${day} is a club day off`, 3000, "error");
      return;
    }

    // Check if the time is within club hours
    if (!isTimeInClubHours(time, clubData.times)) {
      showToast(
        globalDispatch,
        "This time is outside club hours",
        3000,
        "error"
      );
      return;
    }

    // Check if the time is an exception time
    const dayExceptions = getDayExceptions(day, clubData.exceptions);
    if (isExceptionTime(time, dayExceptions)) {
      const exceptionName = getExceptionName(time, dayExceptions);
      showToast(
        globalDispatch,
        `This time is marked as "${exceptionName || "Exception"}"`,
        3000,
        "warning"
      );
      // We still allow selection but show a warning
    }

    setDefaultTimeSlot((prev) => {
      return prev.map((daySlot) => {
        if (daySlot.day === day.toLowerCase()) {
          const timeWithoutSeconds = time.replace(":00", "");
          const timeExists = daySlot.timeslots.some(
            (t) => t === time || t === timeWithoutSeconds
          );
          if (!timeExists) {
            return {
              ...daySlot,
              timeslots: [...daySlot.timeslots, time].sort(),
            };
          }
        }
        return daySlot;
      });
    });
  };

  const handleDeleteTime = (time, day) => {
    setSelectedTimeSlot((prev) => {
      return prev
        .map((daySlot) => {
          if (daySlot.day === day.toLowerCase()) {
            const updatedTimeslots = daySlot.timeslots.filter(
              (t) => t !== time && t !== time.replace(":00", "")
            );

            // If no timeslots left, remove the day entirely
            if (updatedTimeslots.length === 0) {
              return null;
            }

            return {
              ...daySlot,
              timeslots: updatedTimeslots,
            };
          }
          return daySlot;
        })
        .filter(Boolean); // Remove null entries
    });
  };

  const handleDefaultDeleteTime = (time, day) => {
    setDefaultTimeSlot((prev) => {
      return prev.map((daySlot) => {
        if (daySlot.day === day.toLowerCase()) {
          return {
            ...daySlot,
            timeslots: daySlot.timeslots.filter(
              (t) => t !== time && t !== time.replace(":00", "")
            ),
          };
        }
        return daySlot;
      });
    });
  };

  // Update the formatSelectedTimes function
  const formatSelectedTimes = () => {
    if (!selectedTimeSlot) return [];
    return selectedTimeSlot.filter((daySlot) => daySlot.timeslots.length > 0);
  };

  // Format default times
  const formatDefaultTimes = () => {
    if (!defaultTimeSlot) return [];
    return defaultTimeSlot.filter((daySlot) => daySlot.timeslots.length > 0);
  };

  const onSaveChanges = async () => {
    try {
      setSavingTimeslot(true);
      const formattedData = formatSelectedTimes();
      const weekId = getCurrentWeekIdentifier();

      console.log("Saving changes for week:", weekId, formattedData);

      // Only save if there are actual changes
      if (!hasChanges()) {
        console.log("No changes detected, skipping save");
        setSavingTimeslot(false);
        return;
      }

      // Update week-specific availability
      const updatedWeekSpecific = [...weekSpecificAvailability];
      const existingWeekIndex = updatedWeekSpecific.findIndex(
        (week) => week.weekId === weekId
      );

      if (existingWeekIndex !== -1) {
        // Update existing week
        updatedWeekSpecific[existingWeekIndex].availability = formattedData;
      } else {
        // Add new week
        updatedWeekSpecific.push({
          weekId,
          availability: formattedData,
        });
      }

      // Sort by weekId to maintain order
      updatedWeekSpecific.sort((a, b) => a.weekId.localeCompare(b.weekId));

      console.log(
        "Sending updated week-specific availability to server:",
        updatedWeekSpecific
      );

      await sdk.callRawAPI(
        "/v3/api/custom/courtmatchup/coach/profile-edit",
        {
          week_specific_availability: updatedWeekSpecific,
        },
        "POST"
      );

      // Fetch the latest data from the server to ensure we have the most up-to-date information
      const profile = await sdk.callRawAPI(
        "/v3/api/custom/courtmatchup/coach/profile",
        {},
        "GET"
      );

      const week_specific_availability = profile.week_specific_availability
        ? JSON.parse(profile.week_specific_availability)
        : [];

      console.log(
        "Received updated week-specific availability from server:",
        week_specific_availability
      );

      // Update the state with the latest data from the server
      setWeekSpecificAvailability(week_specific_availability);

      // Find the current week's data in the updated data
      const currentWeekData = week_specific_availability.find(
        (week) => week.weekId === weekId
      );

      console.log("Current week data after save:", currentWeekData);

      if (currentWeekData && currentWeekData.availability) {
        // Make a deep copy to avoid reference issues
        const weekSpecificCopy = JSON.parse(
          JSON.stringify(currentWeekData.availability)
        );
        console.log("Setting current week data to:", weekSpecificCopy);
        setSelectedTimeSlot(weekSpecificCopy);
        setOriginalAvailability(weekSpecificCopy);
        setIsCustomizedWeek(true);
      } else {
        console.error("Error: Week data not found after saving!");
      }

      showToast(
        globalDispatch,
        "Week availability updated successfully",
        3000,
        "success"
      );
      setSavingTimeslot(false);
    } catch (error) {
      console.error("Error in onSaveChanges:", error);
      setSavingTimeslot(false);
      showToast(
        globalDispatch,
        "Failed to update week availability",
        3000,
        "error"
      );
    }
  };

  const onSaveDefaultChanges = async () => {
    try {
      setSavingDefaultTimeslot(true);
      const formattedData = formatDefaultTimes();

      console.log("Saving default availability:", formattedData);

      // Only save if there are actual changes
      if (!hasDefaultChanges()) {
        console.log(
          "No changes detected in default availability, skipping save"
        );
        setSavingDefaultTimeslot(false);
        return;
      }

      await sdk.callRawAPI(
        "/v3/api/custom/courtmatchup/coach/profile-edit",
        { default_availability: formattedData },
        "POST"
      );

      // Fetch the latest data from the server
      const profile = await sdk.callRawAPI(
        "/v3/api/custom/courtmatchup/coach/profile",
        {},
        "GET"
      );

      const default_availability = JSON.parse(profile.default_availability);
      console.log(
        "Received updated default availability from server:",
        default_availability
      );

      // Update default availability state
      const defaultAvailability = days.map((day) => ({
        day: day.toLowerCase(),
        timeslots: [],
      }));

      if (default_availability && Array.isArray(default_availability)) {
        default_availability.forEach((dayData) => {
          const index = defaultAvailability.findIndex(
            (day) => day.day === dayData.day.toLowerCase()
          );
          if (index !== -1) {
            defaultAvailability[index].timeslots = [...dayData.timeslots];
          }
        });
      }

      setDefaultTimeSlot(defaultAvailability);
      setOriginalDefaultAvailability(default_availability || []);

      showToast(
        globalDispatch,
        "Default availability updated successfully",
        3000,
        "success"
      );
      setSavingDefaultTimeslot(false);
    } catch (error) {
      console.error("Error in onSaveDefaultChanges:", error);
      setSavingDefaultTimeslot(false);
      showToast(
        globalDispatch,
        "Failed to update default availability",
        3000,
        "error"
      );
    }
  };

  // Function to handle week switching
  const handleWeekSwitch = async (newWeek) => {
    try {
      setIsLoading(true);
      const weekId = newWeek.clone().startOf("week").format("YYYY-MM-DD");

      // Log the week we're switching to for debugging
      console.log(`Switching to week: ${weekId}`);

      // Fetch the latest data from the server to ensure we have the most up-to-date information
      const profile = await sdk.callRawAPI(
        "/v3/api/custom/courtmatchup/coach/profile",
        {},
        "GET"
      );

      const default_availability = JSON.parse(profile.default_availability);
      const week_specific_availability = profile.week_specific_availability
        ? JSON.parse(profile.week_specific_availability)
        : [];

      // Log the fetched data for debugging
      console.log(
        "Fetched week-specific availability:",
        week_specific_availability
      );

      // Update the state with the latest data from the server
      setWeekSpecificAvailability(week_specific_availability);

      // Update default availability state
      const defaultAvailability = days.map((day) => ({
        day: day.toLowerCase(),
        timeslots: [],
      }));

      if (default_availability && Array.isArray(default_availability)) {
        default_availability.forEach((dayData) => {
          const index = defaultAvailability.findIndex(
            (day) => day.day === dayData.day.toLowerCase()
          );
          if (index !== -1) {
            defaultAvailability[index].timeslots = [...dayData.timeslots];
          }
        });
      }

      setDefaultTimeSlot(defaultAvailability);
      setOriginalDefaultAvailability(default_availability || []);

      // Find the current week's data in the updated data
      const weekSpecific = week_specific_availability.find(
        (week) => week.weekId === weekId
      );

      console.log("Week specific data found:", weekSpecific);

      if (weekSpecific && weekSpecific.availability) {
        // Use the specific availability for this week
        // Make a deep copy to avoid reference issues
        const weekSpecificCopy = JSON.parse(
          JSON.stringify(weekSpecific.availability)
        );
        console.log(
          "Found custom schedule for week:",
          weekId,
          weekSpecificCopy
        );
        setSelectedTimeSlot(weekSpecificCopy);
        setOriginalAvailability(weekSpecificCopy);
        setIsCustomizedWeek(true);
      } else {
        // If no specific data for this week, use the default availability
        console.log(
          "No custom schedule found for week:",
          weekId,
          "Using default schedule"
        );
        const defaultAvailabilityCopy = JSON.parse(
          JSON.stringify(defaultAvailability)
        );
        setSelectedTimeSlot(defaultAvailabilityCopy);
        setOriginalAvailability(defaultAvailabilityCopy);
        setIsCustomizedWeek(false);
      }
      setIsLoading(false);
    } catch (error) {
      console.error("Error in handleWeekSwitch:", error);
      setIsLoading(false);
      showToast(
        globalDispatch,
        "Failed to load availability data",
        3000,
        "error"
      );
    }
  };

  const handlePreviousWeek = async () => {
    if (weekCounter > 0) {
      // Check if there are unsaved changes
      if (hasChanges()) {
        const confirmSwitch = window.confirm(
          "You have unsaved changes. Are you sure you want to switch weeks?"
        );
        if (!confirmSwitch) return;
      }

      const newWeekCounter = weekCounter - 1;
      setWeekCounter(newWeekCounter);
      const newWeek = currentWeek.clone().subtract(1, "week");
      setCurrentWeek(newWeek);

      // Load the availability for the previous week
      await handleWeekSwitch(newWeek);
    }
  };

  const handleNextWeek = async () => {
    // Check if there are unsaved changes
    if (hasChanges()) {
      const confirmSwitch = window.confirm(
        "You have unsaved changes. Are you sure you want to switch weeks?"
      );
      if (!confirmSwitch) return;
    }

    const newWeekCounter = weekCounter + 1;
    setWeekCounter(newWeekCounter);
    const newWeek = currentWeek.clone().add(1, "week");
    setCurrentWeek(newWeek);

    // Load the availability for the next week
    await handleWeekSwitch(newWeek);
  };

  const getWeekRange = () => {
    const startOfWeek = currentWeek.clone().startOf("week");
    const endOfWeek = currentWeek.clone().endOf("week");
    const dateRange = `${startOfWeek.format("MMM D")} - ${endOfWeek.format(
      "MMM D"
    )}`;

    if (weekCounter === 0) {
      return `This week (${dateRange})`;
    } else if (weekCounter === 1) {
      return `Next week (${dateRange})`;
    } else {
      return `${weekCounter} weeks from now (${dateRange})`;
    }
  };

  // Update the isSelected check to be more flexible
  const isSelected = (time, day) => {
    const daySlot = selectedTimeSlot?.find(
      (daySlot) => daySlot.day === day.toLowerCase()
    );
    if (!daySlot) return false;

    const timeWithoutSeconds = time.replace(":00", "");
    return daySlot.timeslots.some(
      (t) => t === time || t.replace(":00", "") === timeWithoutSeconds
    );
  };

  const isDefaultSelected = (time, day) => {
    const daySlot = defaultTimeSlot?.find(
      (daySlot) => daySlot.day === day.toLowerCase()
    );
    if (!daySlot) return false;

    const timeWithoutSeconds = time.replace(":00", "");
    return daySlot.timeslots.some(
      (t) => t === time || t.replace(":00", "") === timeWithoutSeconds
    );
  };

  // Parse club data when club changes
  useEffect(() => {
    if (club) {
      const times = club.times ? JSON.parse(club.times) : [];
      const daysOff = club.days_off ? JSON.parse(club.days_off) : [];
      const exceptions = club.exceptions ? JSON.parse(club.exceptions) : [];
      setClubData({ times, daysOff, exceptions });
    }
  }, [club]);

  // Use isMobileView in the component to make it responsive
  useEffect(() => {
    // This is just to make the linter happy since we're using isMobileView in the JSX
    console.log("Mobile view state updated:", isMobileView);
  }, [isMobileView]);

  useEffect(() => {
    fetchCoachProfile();
  }, []);

  // Handle window resize for responsive layout
  useEffect(() => {
    const handleResize = () => {
      setIsMobileView(window.innerWidth < 768);
    };

    window.addEventListener("resize", handleResize);
    return () => {
      window.removeEventListener("resize", handleResize);
    };
  }, []);

  const handleResetButtonClick = () => {
    setIsResetModalOpen(true);
  };

  const handleCloseResetModal = () => {
    setIsResetModalOpen(false);
  };

  const handleResetToDefaults = async () => {
    if (!coachProfile) return;
    setIsLoading(true);
    setIsResetModalOpen(false);
    try {
      // Get current week identifier
      const weekId = getCurrentWeekIdentifier();
      console.log("Resetting week to defaults:", weekId);

      // Ensure weekSpecificAvailability is an array
      const currentWeekSpecific = Array.isArray(weekSpecificAvailability)
        ? weekSpecificAvailability
        : [];

      // Check if this week has custom availability
      const hasCustomAvailability = currentWeekSpecific.some(
        (week) => week.weekId === weekId
      );

      if (!hasCustomAvailability) {
        console.log(
          "This week doesn't have custom availability, nothing to reset"
        );
        setIsLoading(false);
        return;
      }

      // Create an updated week specific availability by removing current week
      const updatedWeekSpecific = currentWeekSpecific.filter(
        (week) => week.weekId !== weekId
      );

      console.log("Removing custom schedule for week:", weekId);
      console.log("Updated week-specific availability:", updatedWeekSpecific);

      await sdk.callRawAPI(
        "/v3/api/custom/courtmatchup/coach/profile-edit",
        { week_specific_availability: updatedWeekSpecific },
        "POST"
      );

      // Fetch the latest data from the server to ensure we have the most up-to-date information
      const profile = await sdk.callRawAPI(
        "/v3/api/custom/courtmatchup/coach/profile",
        {},
        "GET"
      );

      const default_availability = JSON.parse(profile.default_availability);
      const week_specific_availability = profile.week_specific_availability
        ? JSON.parse(profile.week_specific_availability)
        : [];

      console.log("Fetched updated data from server:", {
        default_availability,
        week_specific_availability,
      });

      // Update the state with the latest data from the server
      setWeekSpecificAvailability(week_specific_availability);

      // Create a deep copy of the default availability from the original data
      const defaultAvailabilityData = days.map((day) => ({
        day: day.toLowerCase(),
        timeslots: [],
      }));

      // Fill in with the default availability data
      if (default_availability && Array.isArray(default_availability)) {
        default_availability.forEach((dayData) => {
          const index = defaultAvailabilityData.findIndex(
            (day) => day.day === dayData.day.toLowerCase()
          );
          if (index !== -1) {
            defaultAvailabilityData[index].timeslots = [...dayData.timeslots];
          }
        });
      }

      // Update default availability state
      setDefaultTimeSlot(JSON.parse(JSON.stringify(defaultAvailabilityData)));
      setOriginalDefaultAvailability(default_availability || []);

      // Set the current week to use default availability
      const defaultAvailabilityCopy = JSON.parse(
        JSON.stringify(defaultAvailabilityData)
      );
      setSelectedTimeSlot(defaultAvailabilityCopy);
      setOriginalAvailability(defaultAvailabilityCopy);
      setIsCustomizedWeek(false);

      showToast(
        globalDispatch,
        "Availability reset to defaults successfully",
        3000,
        "success"
      );
      setIsLoading(false);
    } catch (error) {
      console.error("Error in handleResetToDefaults:", error);
      setIsLoading(false);
      showToast(
        globalDispatch,
        "Failed to reset availability to defaults",
        3000,
        "error"
      );
    }
  };

  // Update the tab change handler to reset changes if switching without saving
  const handleTabChange = (newTab) => {
    if (tab === "calendar" && hasChanges() && isCustomizedWeek) {
      // If there are unsaved changes in calendar tab, show confirmation
      if (
        window.confirm(
          "You have unsaved changes. Are you sure you want to switch tabs?"
        )
      ) {
        setTab(newTab);
      }
    } else {
      setTab(newTab);
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 p-3 sm:p-4 md:p-6">
      {isLoading && <LoadingSpinner />}
      <div className="w-full">
        {/* Header */}
        <div className="mb-2 flex flex-wrap items-start justify-between gap-2 sm:flex-nowrap sm:items-center">
          <div className="flex items-center gap-2">
            <h1 className="text-xl font-semibold text-gray-900 sm:text-2xl">
              My Availability
            </h1>
            <div
              data-tooltip-id="availability-help"
              className="cursor-pointer text-gray-400 hover:text-gray-600"
            >
              <FiHelpCircle className="h-5 w-5" />
            </div>
            <Tooltip
              id="availability-help"
              place="right"
              className="z-50 max-w-md rounded-lg border border-gray-200 bg-white p-3 text-gray-800 shadow-lg"
              style={{
                backgroundColor: "#fff",
                border: "1px solid #e4e4e7",
                color: "#000",
                borderRadius: "12px",
                padding: "12px",
                maxWidth: "300px",
                opacity: 1,
              }}
            >
              <div className="text-sm">
                <p className="mb-2">
                  Your availability determines the times users can book lessons
                  with you. Each week, the availability shown in the Calendar
                  tab matches the Default tab.
                </p>
                <p className="mb-2">
                  If you want to change your schedule for just one week, update
                  it in the Calendar tab. Your availability will revert to the
                  default next week.
                </p>
                <p className="mb-2">
                  If you need a more permanent change, update your availability
                  in the Default tab.
                </p>
                <p>
                  If you modify your schedule in the Calendar tab but want to
                  reset it to your default availability, you can click 'Reset to
                  Default'.
                </p>
              </div>
            </Tooltip>
          </div>
          {isCustomizedWeek && tab === "calendar" && (
            <div className="rounded-full bg-blue-100 px-3 py-1 text-xs font-medium text-blue-700">
              Custom schedule
            </div>
          )}
        </div>

        <div
          className={`mb-4 flex flex-col gap-3 sm:mb-6 sm:flex-row sm:items-center ${
            tab === "calendar" ? "sm:justify-between" : "sm:justify-end"
          }`}
        >
          {tab === "calendar" && (
            <div className="flex flex-col gap-3 sm:flex-row sm:items-center">
              <div className="w-full overflow-x-auto sm:w-auto">
                <div className="w-fit max-w-lg rounded-xl bg-white p-1">
                  <div className="flex items-center justify-between gap-2 rounded-xl bg-gray-50 p-2 sm:gap-4">
                    <button
                      onClick={handlePreviousWeek}
                      className={`rounded-xl bg-white p-2 text-gray-600 ${
                        weekCounter === 0
                          ? "cursor-not-allowed opacity-50"
                          : "hover:text-gray-800"
                      }`}
                      aria-label="Previous week"
                    >
                      <svg
                        width="20"
                        height="20"
                        viewBox="0 0 24 24"
                        fill="none"
                        xmlns="http://www.w3.org/2000/svg"
                        className="sm:w-24"
                      >
                        <path
                          d="M15 18L9 12L15 6"
                          stroke="currentColor"
                          strokeWidth="2"
                          strokeLinecap="round"
                          strokeLinejoin="round"
                        />
                      </svg>
                    </button>
                    <div className="text-sm font-medium sm:text-lg">
                      {getWeekRange()}
                    </div>
                    <button
                      onClick={handleNextWeek}
                      className="rounded-xl bg-white p-2 text-gray-600 hover:text-gray-800"
                      aria-label="Next week"
                    >
                      <svg
                        width="20"
                        height="20"
                        viewBox="0 0 24 24"
                        fill="none"
                        xmlns="http://www.w3.org/2000/svg"
                        className="sm:w-24"
                      >
                        <path
                          d="M9 18L15 12L9 6"
                          stroke="currentColor"
                          strokeWidth="2"
                          strokeLinecap="round"
                          strokeLinejoin="round"
                        />
                      </svg>
                    </button>
                  </div>
                </div>
              </div>
              <div>
                <button
                  onClick={handleResetButtonClick}
                  className="flex items-center gap-1 rounded-lg bg-white p-2 text-sm text-gray-600 hover:text-gray-800"
                >
                  <span>Reset to default</span>
                  <span data-tooltip-id="reset-help">
                    <svg
                      width="16"
                      height="16"
                      viewBox="0 0 16 16"
                      fill="none"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        d="M7.16683 7.3335H8.00016L8.00016 10.8335M14.1668 8.00016C14.1668 11.4059 11.4059 14.1668 8.00016 14.1668C4.59441 14.1668 1.8335 11.4059 1.8335 8.00016C1.8335 4.59441 4.59441 1.8335 8.00016 1.8335C11.4059 1.8335 14.1668 4.59441 14.1668 8.00016Z"
                        stroke="#868C98"
                        strokeWidth="1.5"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                      />
                      <path
                        d="M7.5415 5.33333C7.5415 5.58646 7.74671 5.79167 7.99984 5.79167C8.25297 5.79167 8.45817 5.58646 8.45817 5.33333C8.45817 5.0802 8.25297 4.875 7.99984 4.875C7.74671 4.875 7.5415 5.0802 7.5415 5.33333Z"
                        fill="#868C98"
                        stroke="#868C98"
                        strokeWidth="0.25"
                      />
                    </svg>
                  </span>
                </button>
                <Tooltip
                  id="reset-help"
                  place="top"
                  className="z-50 max-w-md rounded-lg border border-gray-200 bg-white p-3 text-gray-800 shadow-lg"
                  style={{
                    backgroundColor: "#fff",
                    border: "1px solid #e4e4e7",
                    color: "#000",
                    borderRadius: "12px",
                    padding: "12px",
                    maxWidth: "300px",
                    opacity: 1,
                  }}
                >
                  <div className="text-sm">
                    <p>
                      Reset this week's availability to match your default
                      availability settings.
                    </p>
                  </div>
                </Tooltip>
              </div>
            </div>
          )}

          <div className="flex items-center justify-end">
            {/* View Toggle */}
            <div className="flex w-full divide-x-2 rounded-lg border border-gray-200 sm:w-auto">
              <button
                onClick={() => handleTabChange("calendar")}
                className={`flex-1 rounded-l-lg px-3 py-2 text-sm sm:flex-none sm:px-4 ${
                  tab === "calendar"
                    ? "bg-white font-medium text-black"
                    : "bg-gray-100 text-gray-500"
                } hover:bg-gray-50`}
              >
                Calendar
              </button>
              <button
                onClick={() => handleTabChange("defaults")}
                className={`flex-1 rounded-r-lg px-3 py-2 text-sm sm:flex-none sm:px-4 ${
                  tab === "defaults"
                    ? "bg-white font-medium text-black"
                    : "bg-gray-100 text-gray-500"
                } hover:bg-gray-50`}
              >
                Defaults
              </button>
            </div>
          </div>
        </div>
        {tab === "calendar" && (
          <>
            {hasChanges() && (
              <div className="mb-4 flex justify-center sm:justify-start">
                <InteractiveButton
                  onClick={onSaveChanges}
                  className="w-full rounded-lg bg-primaryBlue px-4 py-2 text-white sm:w-auto"
                  loading={savingTimeslot}
                >
                  Save changes
                </InteractiveButton>
              </div>
            )}

            <div>
              {isCustomizedWeek && (
                <div className="mb-4 rounded-lg bg-blue-50 p-3 text-sm">
                  <div className="flex flex-col items-start sm:flex-row sm:items-center">
                    <svg
                      className="mb-2 h-5 w-5 text-blue-500 sm:mb-0 sm:mr-2"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth="2"
                        d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                      />
                    </svg>
                    <p>
                      You're viewing a customized schedule for {getWeekRange()}.
                      Any changes you make will only apply to this week.
                    </p>
                  </div>
                </div>
              )}
              <div className="overflow-x-auto pb-4">
                <TimeSlotGrid
                  days={days}
                  isSelected={isSelected}
                  handleTimeSelect={handleTimeSelect}
                  handleDeleteTime={handleDeleteTime}
                  renderTimeSlotContent={(timeSlot, day) => {
                    // Check if day is a club day off
                    if (isClubDayOff(day, clubData.daysOff)) {
                      return (
                        <div className="absolute inset-0 flex items-center justify-center bg-red-50 text-xs font-medium text-red-500">
                          Club Closed
                        </div>
                      );
                    }

                    // Check if time is outside club hours
                    if (!isTimeInClubHours(timeSlot.value, clubData.times)) {
                      return (
                        <div className="absolute inset-0 flex items-center justify-center bg-red-50 text-xs font-medium text-red-500">
                          Club Closed
                        </div>
                      );
                    }

                    // Check if time is an exception
                    const dayExceptions = getDayExceptions(
                      day,
                      clubData.exceptions
                    );
                    if (isExceptionTime(timeSlot.value, dayExceptions)) {
                      const exceptionName = getExceptionName(
                        timeSlot.value,
                        dayExceptions
                      );
                      return (
                        <div className="absolute inset-0 flex items-center justify-center bg-orange-50 text-xs font-medium text-orange-500">
                          <MdCleaningServices className="mr-1 hidden sm:inline" />
                          <span className="truncate">
                            {exceptionName || "Exception"}
                          </span>
                        </div>
                      );
                    }

                    return null;
                  }}
                  disableTimeSlot={(timeSlot, day) => {
                    // Disable if day is a club day off
                    if (isClubDayOff(day, clubData.daysOff)) {
                      return true;
                    }

                    // Disable if time is outside club hours
                    if (!isTimeInClubHours(timeSlot.value, clubData.times)) {
                      return true;
                    }

                    // We don't disable exception times, just show a warning when selected
                    return false;
                  }}
                />
              </div>
            </div>
          </>
        )}

        {tab === "defaults" && (
          <div>
            {hasDefaultChanges() && (
              <div className="mb-4 flex justify-center sm:justify-start">
                <InteractiveButton
                  onClick={onSaveDefaultChanges}
                  className="w-full rounded-lg bg-primaryBlue px-4 py-2 text-white sm:w-auto"
                  loading={savingDefaultTimeslot}
                >
                  Save default changes
                </InteractiveButton>
              </div>
            )}
            <div className="mb-4 rounded-lg bg-yellow-50 p-3 text-sm">
              <div className="flex flex-col items-start sm:flex-row sm:items-center">
                <svg
                  className="mb-2 h-5 w-5 text-yellow-500 sm:mb-0 sm:mr-2"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth="2"
                    d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                  />
                </svg>
                <p>
                  Changes made here will apply to all future weeks unless
                  overridden with a custom weekly schedule.
                </p>
              </div>
            </div>
            <div className="overflow-x-auto pb-4">
              <TimeSlotGrid
                days={days}
                isSelected={isDefaultSelected}
                handleTimeSelect={handleDefaultTimeSelect}
                handleDeleteTime={handleDefaultDeleteTime}
                renderTimeSlotContent={(timeSlot, day) => {
                  // Check if day is a club day off
                  if (isClubDayOff(day, clubData.daysOff)) {
                    return (
                      <div className="absolute inset-0 flex items-center justify-center bg-red-50 text-xs font-medium text-red-500">
                        Club Closed
                      </div>
                    );
                  }

                  // Check if time is outside club hours
                  if (!isTimeInClubHours(timeSlot.value, clubData.times)) {
                    return (
                      <div className="absolute inset-0 flex items-center justify-center bg-red-50 text-xs font-medium text-red-500">
                        Club Closed
                      </div>
                    );
                  }

                  // Check if time is an exception
                  const dayExceptions = getDayExceptions(
                    day,
                    clubData.exceptions
                  );
                  if (isExceptionTime(timeSlot.value, dayExceptions)) {
                    const exceptionName = getExceptionName(
                      timeSlot.value,
                      dayExceptions
                    );
                    return (
                      <div className="absolute inset-0 flex items-center justify-center bg-orange-50 text-xs font-medium text-orange-500">
                        <MdCleaningServices className="mr-1 hidden sm:inline" />
                        <span className="truncate">
                          {exceptionName || "Exception"}
                        </span>
                      </div>
                    );
                  }

                  return null;
                }}
                disableTimeSlot={(timeSlot, day) => {
                  // Disable if day is a club day off
                  if (isClubDayOff(day, clubData.daysOff)) {
                    return true;
                  }

                  // Disable if time is outside club hours
                  if (!isTimeInClubHours(timeSlot.value, clubData.times)) {
                    return true;
                  }

                  // We don't disable exception times, just show a warning when selected
                  return false;
                }}
              />
            </div>
          </div>
        )}
      </div>

      <DeleteModal
        isOpen={isResetModalOpen}
        onClose={handleCloseResetModal}
        onDelete={handleResetToDefaults}
        title="Reset to Default"
        message="Are you sure you want to reset your current week's availability to match your default settings? Any customizations you've made for this week will be lost."
        buttonText="Yes, Reset"
        loading={isLoading}
      />
    </div>
  );
}
